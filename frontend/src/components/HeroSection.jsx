import React from 'react';
import {
  <PERSON>,
  Typography,
  Container,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  Avatar,
  Stack
} from '@mui/material';
import {
  AutoAwesome,
  TrendingUp,
  Speed,
  Security,
  CloudUpload,
  Description,
  Image,
  Code,
  PlayArrow
} from '@mui/icons-material';

const HeroSection = ({ onScrollToUpload }) => {
  const supportedFormats = [
    { icon: <Image />, label: '图像文件', formats: 'PNG, JPG, JPEG, GIF' },
    { icon: <Description />, label: 'PDF文档', formats: '需求文档, 设计文档' },
    { icon: <Code />, label: 'API文档', formats: 'OpenAPI, Swagger, YAML' }
  ];

  const stats = [
    { value: '10,000+', label: '测试用例生成' },
    { value: '500+', label: '企业用户' },
    { value: '99.9%', label: '服务可用性' },
    { value: '90%+', label: '效率提升' }
  ];

  // return (
  //   <Box
  //     sx={{
  //       background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  //       color: 'white',
  //       py: 8,
  //       position: 'relative',
  //       overflow: 'hidden'
  //     }}
  //   >
  //     {/* 背景装饰 */}
  //     <Box
  //       sx={{
  //         position: 'absolute',
  //         top: 0,
  //         left: 0,
  //         right: 0,
  //         bottom: 0,
  //         opacity: 0.1,
  //         background: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
  //       }}
  //     />
  //
  //     {/*<Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>*/}
  //     {/*  <Grid container spacing={6} alignItems="center">*/}
  //     {/*    <Grid item xs={12} md={6}>*/}
  //     {/*      <Box sx={{ mb: 4 }}>*/}
  //     {/*        <Chip*/}
  //     {/*          icon={<AutoAwesome />}*/}
  //     {/*          label="AI驱动 · 智能生成"*/}
  //     {/*          sx={{*/}
  //     {/*            bgcolor: 'rgba(255, 255, 255, 0.2)',*/}
  //     {/*            color: 'white',*/}
  //     {/*            mb: 3,*/}
  //     {/*            '& .MuiChip-icon': { color: 'white' }*/}
  //     {/*          }}*/}
  //     {/*        />*/}
  //     {/*        */}
  //     {/*        <Typography*/}
  //     {/*          variant="h2"*/}
  //     {/*          component="h1"*/}
  //     {/*          sx={{*/}
  //     {/*            fontWeight: 700,*/}
  //     {/*            mb: 2,*/}
  //     {/*            fontSize: { xs: '2.5rem', md: '3.5rem' },*/}
  //     {/*            lineHeight: 1.2*/}
  //     {/*          }}*/}
  //     {/*        >*/}
  //     {/*          革命性的*/}
  //     {/*          <Box component="span" sx={{ color: '#ffd700' }}>*/}
  //     {/*            AI测试用例*/}
  //     {/*          </Box>*/}
  //     {/*          生成平台*/}
  //     {/*        </Typography>*/}
  //
  //     {/*        <Typography*/}
  //     {/*          variant="h5"*/}
  //     {/*          sx={{*/}
  //     {/*            mb: 4,*/}
  //     {/*            opacity: 0.9,*/}
  //     {/*            fontWeight: 400,*/}
  //     {/*            lineHeight: 1.4*/}
  //     {/*          }}*/}
  //     {/*        >*/}
  //     {/*          基于先进多模态AI技术，支持图像、文档、API等多种输入格式，*/}
  //     {/*          智能生成高质量测试用例，助力企业数字化转型*/}
  //     {/*        </Typography>*/}
  //
  //     {/*        <Stack direction="row" spacing={2} sx={{ mb: 4 }}>*/}
  //     {/*          <Button*/}
  //     {/*            variant="contained"*/}
  //     {/*            size="large"*/}
  //     {/*            startIcon={<PlayArrow />}*/}
  //     {/*            onClick={onScrollToUpload}*/}
  //     {/*            sx={{*/}
  //     {/*              bgcolor: 'white',*/}
  //     {/*              color: '#667eea',*/}
  //     {/*              px: 4,*/}
  //     {/*              py: 1.5,*/}
  //     {/*              fontSize: '1.1rem',*/}
  //     {/*              fontWeight: 600,*/}
  //     {/*              '&:hover': {*/}
  //     {/*                bgcolor: '#f5f5f5',*/}
  //     {/*                transform: 'translateY(-2px)',*/}
  //     {/*                boxShadow: '0 8px 25px rgba(0,0,0,0.15)'*/}
  //     {/*              },*/}
  //     {/*              transition: 'all 0.3s ease'*/}
  //     {/*            }}*/}
  //     {/*          >*/}
  //     {/*            立即体验*/}
  //     {/*          </Button>*/}
  //     {/*          */}
  //     {/*          <Button*/}
  //     {/*            variant="outlined"*/}
  //     {/*            size="large"*/}
  //     {/*            sx={{*/}
  //     {/*              borderColor: 'white',*/}
  //     {/*              color: 'white',*/}
  //     {/*              px: 4,*/}
  //     {/*              py: 1.5,*/}
  //     {/*              fontSize: '1.1rem',*/}
  //     {/*              fontWeight: 600,*/}
  //     {/*              '&:hover': {*/}
  //     {/*                borderColor: 'white',*/}
  //     {/*                bgcolor: 'rgba(255, 255, 255, 0.1)'*/}
  //     {/*              }*/}
  //     {/*            }}*/}
  //     {/*          >*/}
  //     {/*            了解更多*/}
  //     {/*          </Button>*/}
  //     {/*        </Stack>*/}
  //
  //     {/*        /!* 支持格式 *!/*/}
  //     {/*        <Box>*/}
  //     {/*          <Typography variant="body2" sx={{ mb: 2, opacity: 0.8 }}>*/}
  //     {/*            支持多种输入格式：*/}
  //     {/*          </Typography>*/}
  //     {/*          <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>*/}
  //     {/*            {supportedFormats.map((format, index) => (*/}
  //     {/*              <Chip*/}
  //     {/*                key={index}*/}
  //     {/*                icon={format.icon}*/}
  //     {/*                label={format.label}*/}
  //     {/*                size="small"*/}
  //     {/*                sx={{*/}
  //     {/*                  bgcolor: 'rgba(255, 255, 255, 0.15)',*/}
  //     {/*                  color: 'white',*/}
  //     {/*                  '& .MuiChip-icon': { color: 'white' }*/}
  //     {/*                }}*/}
  //     {/*              />*/}
  //     {/*            ))}*/}
  //     {/*          </Stack>*/}
  //     {/*        </Box>*/}
  //     {/*      </Box>*/}
  //     {/*    </Grid>*/}
  //
  //     {/*    <Grid item xs={12} md={6}>*/}
  //     {/*      <Box sx={{ textAlign: 'center' }}>*/}
  //     {/*        /!* 数据统计 *!/*/}
  //     {/*        <Grid container spacing={3} sx={{ mb: 4 }}>*/}
  //     {/*          {stats.map((stat, index) => (*/}
  //     {/*            <Grid item xs={6} key={index}>*/}
  //     {/*              <Card*/}
  //     {/*                elevation={0}*/}
  //     {/*                sx={{*/}
  //     {/*                  bgcolor: 'rgba(255, 255, 255, 0.15)',*/}
  //     {/*                  backdropFilter: 'blur(10px)',*/}
  //     {/*                  border: '1px solid rgba(255, 255, 255, 0.2)',*/}
  //     {/*                  borderRadius: 3*/}
  //     {/*                }}*/}
  //     {/*              >*/}
  //     {/*                <CardContent sx={{ textAlign: 'center', py: 3 }}>*/}
  //     {/*                  <Typography*/}
  //     {/*                    variant="h4"*/}
  //     {/*                    sx={{*/}
  //     {/*                      fontWeight: 700,*/}
  //     {/*                      color: '#ffd700',*/}
  //     {/*                      mb: 1*/}
  //     {/*                    }}*/}
  //     {/*                  >*/}
  //     {/*                    {stat.value}*/}
  //     {/*                  </Typography>*/}
  //     {/*                  <Typography*/}
  //     {/*                    variant="body2"*/}
  //     {/*                    sx={{ color: 'white', opacity: 0.9 }}*/}
  //     {/*                  >*/}
  //     {/*                    {stat.label}*/}
  //     {/*                  </Typography>*/}
  //     {/*                </CardContent>*/}
  //     {/*              </Card>*/}
  //     {/*            </Grid>*/}
  //     {/*          ))}*/}
  //     {/*        </Grid>*/}
  //
  //     {/*        /!* 功能亮点 *!/*/}
  //     {/*        <Card*/}
  //     {/*          elevation={0}*/}
  //     {/*          sx={{*/}
  //     {/*            bgcolor: 'rgba(255, 255, 255, 0.1)',*/}
  //     {/*            backdropFilter: 'blur(10px)',*/}
  //     {/*            border: '1px solid rgba(255, 255, 255, 0.2)',*/}
  //     {/*            borderRadius: 3*/}
  //     {/*          }}*/}
  //     {/*        >*/}
  //     {/*          <CardContent sx={{ p: 3 }}>*/}
  //     {/*            <Typography variant="h6" sx={{ color: 'white', mb: 2, fontWeight: 600 }}>*/}
  //     {/*              🎯 核心优势*/}
  //     {/*            </Typography>*/}
  //     {/*            <Grid container spacing={2}>*/}
  //     {/*              <Grid item xs={6}>*/}
  //     {/*                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>*/}
  //     {/*                  <Speed sx={{ color: '#ffd700', mr: 1, fontSize: 20 }} />*/}
  //     {/*                  <Typography variant="body2" sx={{ color: 'white' }}>*/}
  //     {/*                    秒级生成*/}
  //     {/*                  </Typography>*/}
  //     {/*                </Box>*/}
  //     {/*              </Grid>*/}
  //     {/*              <Grid item xs={6}>*/}
  //     {/*                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>*/}
  //     {/*                  <Security sx={{ color: '#ffd700', mr: 1, fontSize: 20 }} />*/}
  //     {/*                  <Typography variant="body2" sx={{ color: 'white' }}>*/}
  //     {/*                    质量保证*/}
  //     {/*                  </Typography>*/}
  //     {/*                </Box>*/}
  //     {/*              </Grid>*/}
  //     {/*              <Grid item xs={6}>*/}
  //     {/*                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>*/}
  //     {/*                  <TrendingUp sx={{ color: '#ffd700', mr: 1, fontSize: 20 }} />*/}
  //     {/*                  <Typography variant="body2" sx={{ color: 'white' }}>*/}
  //     {/*                    效率提升90%+*/}
  //     {/*                  </Typography>*/}
  //     {/*                </Box>*/}
  //     {/*              </Grid>*/}
  //     {/*              <Grid item xs={6}>*/}
  //     {/*                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>*/}
  //     {/*                  <CloudUpload sx={{ color: '#ffd700', mr: 1, fontSize: 20 }} />*/}
  //     {/*                  <Typography variant="body2" sx={{ color: 'white' }}>*/}
  //     {/*                    多格式支持*/}
  //     {/*                  </Typography>*/}
  //     {/*                </Box>*/}
  //     {/*              </Grid>*/}
  //     {/*            </Grid>*/}
  //     {/*          </CardContent>*/}
  //     {/*        </Card>*/}
  //     {/*      </Box>*/}
  //     {/*    </Grid>*/}
  //     {/*  </Grid>*/}
  //     {/*</Container>*/}
  //   </Box>
  // );
};

export default HeroSection;
