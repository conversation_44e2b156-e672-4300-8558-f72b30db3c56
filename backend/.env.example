# 测试用例生成器配置文件示例
# 复制此文件为 .env 并填入实际值

# =============================================================================
# API 配置
# =============================================================================
TESTCASE_QWEN_API_KEY=sk-61aa107ff9204b458c4b2aba74b93e47
TESTCASE_DEEPSEEK_API_KEY=***********************************
TESTCASE_QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
TESTCASE_DEEPSEEK_BASE_URL=https://api.deepseek.com/v1

# =============================================================================
# 服务器配置
# =============================================================================
TESTCASE_HOST=0.0.0.0
TESTCASE_PORT=8000
TESTCASE_DEBUG=false

# =============================================================================
# 安全配置
# =============================================================================
# CORS 允许的来源，用逗号分隔
TESTCASE_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# 最大文件大小（字节）- 默认 50MB
TESTCASE_MAX_FILE_SIZE=52428800

# 允许的文件扩展名，用逗号分隔
TESTCASE_ALLOWED_FILE_EXTENSIONS=.png,.jpg,.jpeg,.gif,.bmp,.webp,.pdf,.json,.yaml,.yml

# =============================================================================
# 文件存储配置
# =============================================================================
TESTCASE_UPLOAD_DIR=uploads
TESTCASE_RESULTS_DIR=results
TESTCASE_TEMP_DIR=temp
TESTCASE_FILE_CLEANUP_DAYS=7

# =============================================================================
# AI 服务配置
# =============================================================================
# AI 请求超时时间（秒）
TESTCASE_AI_REQUEST_TIMEOUT=300

# 最大并发请求数
TESTCASE_MAX_CONCURRENT_REQUESTS=10

# =============================================================================
# 日志配置
# =============================================================================
TESTCASE_LOG_LEVEL=INFO
TESTCASE_LOG_FILE=logs/app.log
TESTCASE_LOG_MAX_SIZE=10485760
TESTCASE_LOG_BACKUP_COUNT=5
