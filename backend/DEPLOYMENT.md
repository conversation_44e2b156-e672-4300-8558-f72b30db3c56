# 测试用例生成器部署指南

## 🚀 快速开始

### 1. 环境准备

确保您的系统已安装：
- Python 3.11 或更高版本
- pip 包管理器

### 2. 安装依赖

```bash
cd backend
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

### 3. 配置环境变量

复制环境变量示例文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的参数：

```env
# 必须配置的API密钥
TESTCASE_QWEN_API_KEY=your_qwen_api_key_here
TESTCASE_DEEPSEEK_API_KEY=your_deepseek_api_key_here

# 可选配置
TESTCASE_HOST=0.0.0.0
TESTCASE_PORT=8000
TESTCASE_DEBUG=false
```

### 4. 启动服务

使用启动脚本：
```bash
python start.py
```

或直接使用 uvicorn：
```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## 📋 配置说明

### 环境变量详解

| 变量名 | 必需 | 默认值 | 说明 |
|--------|------|--------|------|
| `TESTCASE_QWEN_API_KEY` | ✅ | - | Qwen API密钥 |
| `TESTCASE_DEEPSEEK_API_KEY` | ✅ | - | DeepSeek API密钥 |
| `TESTCASE_HOST` | ❌ | 0.0.0.0 | 服务器主机地址 |
| `TESTCASE_PORT` | ❌ | 8000 | 服务器端口 |
| `TESTCASE_DEBUG` | ❌ | false | 调试模式 |
| `TESTCASE_ALLOWED_ORIGINS` | ❌ | localhost:3000 | CORS允许的来源 |
| `TESTCASE_MAX_FILE_SIZE` | ❌ | 52428800 | 最大文件大小(50MB) |
| `TESTCASE_LOG_LEVEL` | ❌ | INFO | 日志级别 |
| `TESTCASE_LOG_FILE` | ❌ | - | 日志文件路径 |

### 安全配置

1. **API密钥管理**：
   - 不要在代码中硬编码API密钥
   - 使用环境变量或密钥管理服务
   - 定期轮换API密钥

2. **CORS配置**：
   - 生产环境中不要使用 `*` 作为允许来源
   - 明确指定允许的域名

3. **文件上传安全**：
   - 系统会自动验证文件类型和大小
   - 上传的文件会被重命名以防止安全问题

## 🔧 生产部署

### 使用 Docker

创建 `Dockerfile`：
```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["python", "start.py"]
```

构建和运行：
```bash
docker build -t testcase-generator .
docker run -p 8000:8000 --env-file .env testcase-generator
```

### 使用 Nginx 反向代理

Nginx 配置示例：
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 支持大文件上传
        client_max_body_size 50M;
    }
}
```

### 使用 Systemd 服务

创建服务文件 `/etc/systemd/system/testcase-generator.service`：
```ini
[Unit]
Description=Test Case Generator API
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/backend
Environment=PATH=/path/to/venv/bin
ExecStart=/path/to/venv/bin/python start.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用和启动服务：
```bash
sudo systemctl enable testcase-generator
sudo systemctl start testcase-generator
```

## 📊 监控和日志

### 健康检查

- 基本检查: `GET /api/ping`
- 详细检查: `GET /api/health`

### 日志配置

日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL

配置日志文件：
```env
TESTCASE_LOG_FILE=logs/app.log
TESTCASE_LOG_LEVEL=INFO
```

### 性能监控

系统提供以下监控指标：
- 请求响应时间（响应头 `X-Process-Time`）
- 文件上传统计
- AI服务调用统计
- 错误率统计

## 🔍 故障排除

### 常见问题

1. **API密钥错误**
   ```
   ConfigurationError: Qwen API密钥未配置
   ```
   解决：检查 `.env` 文件中的API密钥配置

2. **文件上传失败**
   ```
   FileError: 文件大小超过限制
   ```
   解决：调整 `TESTCASE_MAX_FILE_SIZE` 配置

3. **CORS错误**
   ```
   Access to fetch at 'http://localhost:8000' from origin 'http://localhost:3000' has been blocked by CORS policy
   ```
   解决：在 `TESTCASE_ALLOWED_ORIGINS` 中添加前端域名

### 调试模式

启用调试模式：
```env
TESTCASE_DEBUG=true
TESTCASE_LOG_LEVEL=DEBUG
```

调试模式下会提供：
- 详细的错误信息
- 彩色日志输出
- 自动重载功能

## 📈 性能优化

1. **文件处理优化**：
   - 系统使用异步文件处理
   - 大文件分块处理避免内存问题

2. **AI服务优化**：
   - 智能模型选择
   - 请求超时控制
   - 错误重试机制

3. **缓存策略**：
   - 文件哈希去重
   - 结果缓存（可扩展）

## 🔒 安全最佳实践

1. 定期更新依赖包
2. 使用HTTPS（生产环境）
3. 实施访问控制
4. 监控异常活动
5. 定期备份数据
6. 限制文件上传大小和类型
