#!/usr/bin/env python3
"""
将日志文件中的Unicode转义序列转换为中文字符
"""
import re
import json
import shutil
from pathlib import Path

def convert_unicode_in_text(text):
    """转换文本中的Unicode转义序列为中文字符"""
    try:
        # 处理JSON字符串中的Unicode转义序列
        if '\\u' in text:
            # 使用正则表达式找到所有Unicode转义序列
            def replace_unicode(match):
                try:
                    code_point = int(match.group(1), 16)
                    # 避免代理对问题
                    if 0xD800 <= code_point <= 0xDFFF:
                        return match.group(0)
                    return chr(code_point)
                except (ValueError, OverflowError):
                    return match.group(0)
            
            # 替换所有Unicode转义序列
            text = re.sub(r'\\u([0-9a-fA-F]{4})', replace_unicode, text)
    except Exception as e:
        print(f"转换Unicode时出错: {e}")
    
    return text

def process_log_file(file_path):
    """处理日志文件"""
    log_path = Path(file_path)
    
    if not log_path.exists():
        print(f"文件不存在: {file_path}")
        return False
    
    # 创建备份
    backup_path = log_path.with_suffix(log_path.suffix + '.unicode_backup')
    shutil.copy2(log_path, backup_path)
    print(f"已创建备份: {backup_path}")
    
    try:
        # 读取文件
        with open(log_path, 'r', encoding='utf-8', errors='replace') as f:
            content = f.read()
        
        # 转换Unicode转义序列
        converted_content = convert_unicode_in_text(content)
        
        # 统计转换数量
        original_unicode_count = content.count('\\u')
        converted_unicode_count = converted_content.count('\\u')
        converted_count = original_unicode_count - converted_unicode_count
        
        # 写入转换后的内容
        with open(log_path, 'w', encoding='utf-8', errors='replace') as f:
            f.write(converted_content)
        
        print(f"✓ 处理完成: {file_path}")
        print(f"  - 转换了 {converted_count} 个Unicode转义序列")
        print(f"  - 剩余 {converted_unicode_count} 个未转换的序列")
        
        return True
        
    except Exception as e:
        print(f"✗ 处理失败: {e}")
        # 恢复备份
        shutil.copy2(backup_path, log_path)
        print("已恢复原始文件")
        return False

def main():
    """主函数"""
    print("开始转换日志文件中的Unicode转义序列...")
    print("=" * 60)
    
    # 要处理的日志文件
    log_files = [
        "logs/app.log",
        "logs/unicode_test.log"
    ]
    
    success_count = 0
    
    for log_file in log_files:
        print(f"\n处理文件: {log_file}")
        if process_log_file(log_file):
            success_count += 1
    
    print("\n" + "=" * 60)
    print(f"转换完成! 成功处理 {success_count}/{len(log_files)} 个文件")
    
    if success_count > 0:
        print("\n建议:")
        print("1. 检查转换后的日志文件确认中文显示正确")
        print("2. 如果满意结果，可以删除 .unicode_backup 备份文件")
        print("3. 新的日志配置将自动处理后续的Unicode编码问题")

if __name__ == "__main__":
    main()
