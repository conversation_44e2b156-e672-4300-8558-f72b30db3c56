"""
日志配置模块
"""
import logging
import logging.handlers
import sys
import locale
from pathlib import Path
from typing import Optional
from config import settings


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


def setup_logging():
    """设置日志配置"""

    # 设置系统编码以支持中文
    try:
        # 尝试设置UTF-8编码
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8')
        if hasattr(sys.stderr, 'reconfigure'):
            sys.stderr.reconfigure(encoding='utf-8')
    except Exception:
        # 如果重新配置失败，尝试设置locale
        try:
            locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
        except locale.Error:
            try:
                locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
            except locale.Error:
                pass  # 使用系统默认设置

    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.log_level.upper()))

    # 清除现有的处理器
    root_logger.handlers.clear()

    # 日志格式
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    detailed_format = "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s"

    # 控制台处理器 - 确保支持UTF-8编码
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)

    # 设置控制台处理器的编码
    if hasattr(console_handler.stream, 'encoding'):
        console_handler.stream.encoding = 'utf-8'
    
    # 创建支持Unicode的彩色格式化器
    class UnicodeColoredFormatter(ColoredFormatter):
        def format(self, record):
            # 先调用父类的格式化
            formatted = super().format(record)
            # 处理Unicode转义序列
            return self._decode_unicode_escapes(formatted)

        def _decode_unicode_escapes(self, text):
            """解码Unicode转义序列"""
            try:
                # 处理类似 \u4f60\u662f 这样的Unicode转义序列
                if '\\u' in text:
                    # 使用正则表达式替换所有Unicode转义序列
                    import re
                    def replace_unicode(match):
                        try:
                            return chr(int(match.group(1), 16))
                        except ValueError:
                            return match.group(0)

                    text = re.sub(r'\\u([0-9a-fA-F]{4})', replace_unicode, text)
            except Exception:
                # 如果解码失败，保持原样
                pass
            return text

    # 创建支持Unicode的普通格式化器
    class UnicodeFormatter(logging.Formatter):
        def format(self, record):
            formatted = super().format(record)
            return self._decode_unicode_escapes(formatted)

        def _decode_unicode_escapes(self, text):
            """解码Unicode转义序列"""
            try:
                # 处理类似 \u4f60\u662f 这样的Unicode转义序列
                if '\\u' in text:
                    # 使用正则表达式替换所有Unicode转义序列
                    import re
                    def replace_unicode(match):
                        try:
                            return chr(int(match.group(1), 16))
                        except ValueError:
                            return match.group(0)

                    text = re.sub(r'\\u([0-9a-fA-F]{4})', replace_unicode, text)
            except Exception:
                # 如果解码失败，保持原样
                pass
            return text

    if settings.debug:
        # 调试模式使用彩色格式和详细信息
        console_formatter = UnicodeColoredFormatter(detailed_format)
    else:
        # 生产模式使用简单格式
        console_formatter = UnicodeFormatter(log_format)

    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # 文件处理器（如果配置了日志文件）
    if settings.log_file:
        # 确保日志目录存在
        log_file_path = Path(settings.log_file)
        log_file_path.parent.mkdir(parents=True, exist_ok=True)

        # 使用轮转文件处理器，确保UTF-8编码
        file_handler = logging.handlers.RotatingFileHandler(
            filename=settings.log_file,
            maxBytes=settings.log_max_size,
            backupCount=settings.log_backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)

        # 文件日志使用详细格式和Unicode格式化器
        file_formatter = UnicodeFormatter(detailed_format)
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("fastapi").setLevel(logging.INFO)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("autogen").setLevel(logging.WARNING)
    
    # 记录启动信息
    logger = logging.getLogger(__name__)
    logger.info("日志系统初始化完成")
    logger.info(f"日志级别: {settings.log_level}")
    if settings.log_file:
        logger.info(f"日志文件: {settings.log_file}")


def get_logger(name: str) -> logging.Logger:
    """获取指定名称的日志记录器"""
    return logging.getLogger(name)


class RequestLogger:
    """请求日志记录器"""
    
    def __init__(self):
        self.logger = get_logger("request")
    
    def log_request(self, method: str, url: str, client_ip: str, user_agent: Optional[str] = None):
        """记录请求信息"""
        self.logger.info(
            f"Request: {method} {url} from {client_ip} "
            f"User-Agent: {user_agent or 'Unknown'}"
        )
    
    def log_response(self, method: str, url: str, status_code: int, duration: float):
        """记录响应信息"""
        self.logger.info(
            f"Response: {method} {url} - {status_code} - {duration:.3f}s"
        )
    
    def log_error(self, method: str, url: str, error: Exception):
        """记录错误信息"""
        self.logger.error(
            f"Error: {method} {url} - {type(error).__name__}: {str(error)}"
        )


class SecurityLogger:
    """安全日志记录器"""
    
    def __init__(self):
        self.logger = get_logger("security")
    
    def log_file_upload(self, filename: str, file_size: int, client_ip: str, success: bool):
        """记录文件上传"""
        status = "SUCCESS" if success else "FAILED"
        self.logger.info(
            f"File Upload {status}: {filename} ({file_size} bytes) from {client_ip}"
        )
    
    def log_suspicious_activity(self, activity: str, client_ip: str, details: str = ""):
        """记录可疑活动"""
        self.logger.warning(
            f"Suspicious Activity: {activity} from {client_ip} - {details}"
        )
    
    def log_rate_limit_exceeded(self, client_ip: str, endpoint: str):
        """记录频率限制超出"""
        self.logger.warning(
            f"Rate Limit Exceeded: {client_ip} on {endpoint}"
        )


class AIServiceLogger:
    """AI服务日志记录器"""
    
    def __init__(self):
        self.logger = get_logger("ai_service")
    
    def log_generation_start(self, file_type: str, model: str, context_length: int):
        """记录生成开始"""
        self.logger.info(
            f"AI Generation Started: {file_type} file using {model} model, "
            f"context length: {context_length}"
        )
    
    def log_generation_complete(self, duration: float, output_length: int):
        """记录生成完成"""
        self.logger.info(
            f"AI Generation Completed: {duration:.2f}s, output length: {output_length}"
        )
    
    def log_generation_error(self, error: Exception, file_type: str, model: str):
        """记录生成错误"""
        self.logger.error(
            f"AI Generation Error: {type(error).__name__}: {str(error)} "
            f"(file_type: {file_type}, model: {model})"
        )


# 创建全局日志记录器实例
request_logger = RequestLogger()
security_logger = SecurityLogger()
ai_service_logger = AIServiceLogger()
