#!/usr/bin/env python3
"""
修复现有日志文件中的Unicode编码问题
"""
import re
import shutil
from pathlib import Path

def decode_unicode_escapes(text):
    """解码Unicode转义序列"""
    try:
        # 处理类似 \u4f60\u662f 这样的Unicode转义序列
        if '\\u' in text:
            def replace_unicode(match):
                try:
                    code_point = int(match.group(1), 16)
                    # 避免代理对问题，只处理基本多文种平面的字符
                    if 0xD800 <= code_point <= 0xDFFF:
                        return match.group(0)  # 保持原样
                    return chr(code_point)
                except (ValueError, OverflowError):
                    return match.group(0)

            text = re.sub(r'\\u([0-9a-fA-F]{4})', replace_unicode, text)
    except Exception:
        # 如果解码失败，保持原样
        pass
    return text

def fix_log_file(log_file_path):
    """修复日志文件中的Unicode编码问题"""
    log_path = Path(log_file_path)
    
    if not log_path.exists():
        print(f"日志文件不存在: {log_file_path}")
        return False
    
    # 创建备份
    backup_path = log_path.with_suffix(log_path.suffix + '.backup')
    shutil.copy2(log_path, backup_path)
    print(f"已创建备份文件: {backup_path}")
    
    # 读取原始文件
    try:
        with open(log_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        # 如果UTF-8解码失败，尝试其他编码
        try:
            with open(log_path, 'r', encoding='latin1') as f:
                content = f.read()
        except Exception as e:
            print(f"无法读取日志文件: {e}")
            return False
    
    # 处理Unicode转义序列
    original_lines = content.split('\n')
    fixed_lines = []
    unicode_fixes = 0
    
    for line in original_lines:
        if '\\u' in line:
            fixed_line = decode_unicode_escapes(line)
            if fixed_line != line:
                unicode_fixes += 1
            fixed_lines.append(fixed_line)
        else:
            fixed_lines.append(line)
    
    # 写入修复后的内容
    try:
        with open(log_path, 'w', encoding='utf-8', errors='replace') as f:
            f.write('\n'.join(fixed_lines))

        print(f"✓ 修复完成: {log_file_path}")
        print(f"  - 处理了 {unicode_fixes} 行包含Unicode转义序列的日志")
        print(f"  - 总行数: {len(original_lines)}")
        return True

    except Exception as e:
        print(f"写入修复后的文件失败: {e}")
        # 恢复备份
        shutil.copy2(backup_path, log_path)
        print(f"已从备份恢复原始文件")
        return False

def main():
    """主函数"""
    print("开始修复日志文件中的Unicode编码问题...")
    print("=" * 50)
    
    # 要处理的日志文件列表
    log_files = [
        "logs/app.log",
        "logs/unicode_test.log"
    ]
    
    success_count = 0
    total_count = len(log_files)
    
    for log_file in log_files:
        print(f"\n处理文件: {log_file}")
        if fix_log_file(log_file):
            success_count += 1
        else:
            print(f"✗ 处理失败: {log_file}")
    
    print("\n" + "=" * 50)
    print(f"修复完成! 成功处理 {success_count}/{total_count} 个文件")
    
    if success_count > 0:
        print("\n建议:")
        print("1. 检查修复后的日志文件确认中文字符显示正确")
        print("2. 如果满意修复结果，可以删除 .backup 备份文件")
        print("3. 重启应用程序以使用新的日志配置")

if __name__ == "__main__":
    main()
