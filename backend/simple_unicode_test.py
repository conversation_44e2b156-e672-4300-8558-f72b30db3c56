#!/usr/bin/env python3
"""
简化的Unicode日志编码测试脚本
"""
import logging
import logging.handlers
import sys
from pathlib import Path

# 创建支持Unicode的格式化器
class UnicodeFormatter(logging.Formatter):
    def format(self, record):
        formatted = super().format(record)
        return self._decode_unicode_escapes(formatted)

    def _decode_unicode_escapes(self, text):
        """解码Unicode转义序列"""
        try:
            # 处理类似 \u4f60\u662f 这样的Unicode转义序列
            if '\\u' in text:
                # 使用正则表达式替换所有Unicode转义序列
                import re
                def replace_unicode(match):
                    try:
                        return chr(int(match.group(1), 16))
                    except ValueError:
                        return match.group(0)

                text = re.sub(r'\\u([0-9a-fA-F]{4})', replace_unicode, text)
        except Exception:
            # 如果解码失败，保持原样
            pass
        return text

def setup_test_logging():
    """设置测试日志配置"""
    
    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    
    # 清除现有的处理器
    root_logger.handlers.clear()
    
    # 日志格式
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_formatter = UnicodeFormatter(log_format)
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # 文件处理器
    log_file = "logs/unicode_test.log"
    log_file_path = Path(log_file)
    log_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    file_handler = logging.handlers.RotatingFileHandler(
        filename=log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_formatter = UnicodeFormatter(log_format)
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)

def test_unicode_logging():
    """测试Unicode字符在日志中的显示"""
    
    # 初始化日志系统
    setup_test_logging()
    
    # 获取测试日志记录器
    logger = logging.getLogger("unicode_test")
    
    # 测试各种Unicode字符
    test_messages = [
        "这是一个中文测试消息",
        "AI大模型启航课程测试",
        "Transformer神经网络架构解析",
        "测试特殊字符：🚀 💡 🔍 📊",
        "混合语言测试: English + 中文 + 日本語",
        "包含转义序列的消息: \\u4f60\\u597d\\u4e16\\u754c",
        "JSON格式测试: {\"name\": \"张三\", \"age\": 25}",
        "错误信息测试：文件上传失败 - 格式不支持",
    ]
    
    print("开始测试Unicode日志编码...")
    print("=" * 50)
    
    for i, message in enumerate(test_messages, 1):
        logger.info(f"测试消息 {i}: {message}")
        print(f"✓ 已记录测试消息 {i}")
    
    # 测试不同日志级别
    logger.debug("调试信息：系统初始化完成")
    logger.warning("警告信息：内存使用率较高")
    logger.error("错误信息：数据库连接失败")
    
    # 模拟实际应用中的Unicode转义序列
    unicode_escape_message = "\\u4f60\\u662f\\u4e00\\u4e2a\\u4e13\\u4e1a\\u7684\\u6d4b\\u8bd5\\u7528\\u4f8b\\u751f\\u6210\\u5668"
    logger.info(f"Unicode转义测试: {unicode_escape_message}")
    
    print("=" * 50)
    print("Unicode日志测试完成！")
    print("请检查日志文件 logs/unicode_test.log 确认中文字符显示正确")

if __name__ == "__main__":
    test_unicode_logging()
