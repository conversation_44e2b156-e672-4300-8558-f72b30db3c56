# 核心框架
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6

# 数据处理
pandas>=2.0.0
openpyxl==3.1.2
xlsxwriter==3.2.0

# 配置和环境
python-dotenv==1.0.1
pydantic>=2.0.0,<3.0.0
pydantic-settings>=2.0.0

# 异步文件处理
aiofiles==23.2.1

# AI 和自动生成
autogen-agentchat==0.6.4
autogen-ext[openai]==0.6.4

# PDF处理
PyPDF2==3.0.1
pdfplumber==0.10.0

# OpenAPI/Swagger解析
pyyaml==6.0.1
jsonschema==4.21.1

# 安全和验证
cryptography>=41.0.0
passlib[bcrypt]>=1.7.4

# 日志和监控
structlog>=23.0.0

# 开发和测试工具（可选）
pytest>=7.0.0
pytest-asyncio>=0.21.0
httpx>=0.25.0

